<!DOCTYPE html>
<html lang="en">
    
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <title>Resume Website</title>
</head>
<body>
    <header class="navbar">
        <div class="nav-container">
            <a href="index.html" class="logo" aria-label="<PERSON> Antonio V Tan - Home">
                <span class="logo-text">@leugimnat</span>
            </a>

            <nav class="nav-menu" role="navigation" aria-label="Main navigation">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="#home" class="nav-link active" aria-current="page">
                            <i class="fa-solid fa-house"></i>
                            <span>Home</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="education.html" class="nav-link">
                            <i class="fa-solid fa-graduation-cap"></i>
                            <span>Education</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#experience" class="nav-link">
                            <i class="fa-solid fa-briefcase"></i>
                            <span>Experience</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#skills" class="nav-link">
                            <i class="fa-solid fa-code"></i>
                            <span>Skills</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#portfolio" class="nav-link">
                            <i class="fa-solid fa-folder-open"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#contact" class="nav-link">
                            <i class="fa-solid fa-envelope"></i>
                            <span>Contact</span>
                        </a>
                    </li>
                </ul>
            </nav>

            <button type="button" class="mobile-menu-toggle" aria-label="Toggle mobile menu" aria-expanded="false">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </header>
    <section id="home" class="home">
        <div class="home-img">
            <img src="images/pt1.JPG" alt="Picture of Miguel Antonio V Tan in Taiwan." loading="lazy" decoding="async">
        </div>
        <div class="home-content">
            <div class="intro-section">
                <h1>Hey, I'm <span>Miguel</span>, nice to meet you!</h1>
                <h3 class="typing-text">I'm an aspiring <span></span></h3>
                <p class="intro-description">
                    BS Information Technology graduate from Xavier University - Ateneo de Cagayan with a passion for
                    networking, software development, web technologies, and project management.
                </p>
            </div>

            <div class="about-section">
                <h2>About <span>Me</span></h2>
                <div class="about-content">
                    <p>
                        Growing up, I've always had an obsession with technology. It started with a Sega Megadrive,
                        then a PlayStation Portable. Enamored with games, I soon discovered personal computers,
                        and from there my interest in technology flourished.
                    </p>
                    <p>
                        I first started to take interest in programming during high school in our ICT class.
                        We were introduced to Minecraft and played a mini-game involving building literal blocks
                        of code to make the character Steve move around.
                    </p>
                    <p>
                        Since then, I've been engaged beyond measure—playing games, learning software, and applying
                        what I've learned to create projects. In college, I found my true calling, and I'm now
                        on my way to becoming a full-stack developer.
                    </p>
                </div>
            </div>

            <div class="contact-section">
                <div class="social-icons">
                    <a href="https://www.linkedin.com/in/miguel-antonio-tan-945499255/" aria-label="LinkedIn Profile">
                        <i class="fa-brands fa-linkedin"></i>
                    </a>
                    <a href="https://github.com/leugimnat" aria-label="GitHub Profile">
                        <i class="fa-brands fa-github"></i>
                    </a>
                    <a href="https://www.facebook.com/zeuqzavleugimnat/" aria-label="Facebook Profile">
                        <i class="fa-brands fa-facebook"></i>
                    </a>
                </div>
                <a href="#" class="btn">Hire Me</a>
            </div>
        </div>
    </section>
</body>
<footer class="site-footer">
  <div class="footer-inner">
    <div class="footer-main">
      <div class="footer-brand">
        <h3 class="footer-logo">@leugimnat</h3>
        <p class="footer-name">&copy; <span id="year"></span> Miguel Antonio V Tan</p>
        <p class="footer-status">
          <span class="status-indicator"></span>
          Open to Front-End / Full-Stack Roles
        </p>
      </div>

      <div class="footer-links">
        <div class="footer-contact">
          <h4>Get In Touch</h4>
          <div class="contact-info">
            <a href="mailto:<EMAIL>" class="contact-email">
              <i class="fa-solid fa-envelope"></i>
              <EMAIL>
            </a>
            <div class="contact-location">
              <i class="fa-solid fa-location-dot"></i>
              <span>Cagayan de Oro City, Philippines</span>
            </div>
          </div>
        </div>

        <div class="footer-social">
          <h4>Connect</h4>
          <div class="social-links">
            <a href="https://github.com/leugimnat" aria-label="GitHub Profile" title="GitHub">
              <i class="fa-brands fa-github"></i>
              <span>GitHub</span>
            </a>
            <a href="https://www.linkedin.com/in/miguel-antonio-tan-945499255/" aria-label="LinkedIn Profile" title="LinkedIn">
              <i class="fa-brands fa-linkedin"></i>
              <span>LinkedIn</span>
            </a>
            <a href="https://www.facebook.com/zeuqzavleugimnat/" aria-label="Facebook Profile" title="Facebook">
              <i class="fa-brands fa-facebook"></i>
              <span>Facebook</span>
            </a>
          </div>
        </div>
      </div>
    </div>

    <div class="footer-bottom">
      <div class="footer-tech">
        <span class="tech-label">Tech Stack:</span>
        <div class="tech-stack">
          <span class="tech-item">HTML</span>
          <span class="tech-item">CSS</span>
          <span class="tech-item">JavaScript</span>
          <span class="tech-item">MERN</span>
          <span class="tech-item">MEAN</span>
        </div>
      </div>
      <p class="footer-note">
        <i class="fa-solid fa-heart"></i>
        Crafted with care • Accessible & responsive • Last updated Aug 2025
      </p>
    </div>
  </div>
</footer>
<script>
  // Update year
  document.getElementById('year').textContent = new Date().getFullYear();

  // Mobile menu functionality
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const navMenu = document.querySelector('.nav-menu');
  const navLinks = document.querySelectorAll('.nav-link');
  const navbar = document.querySelector('.navbar');

  // Toggle mobile menu
  mobileMenuToggle.addEventListener('click', () => {
    mobileMenuToggle.classList.toggle('active');
    navMenu.classList.toggle('active');

    // Update aria-expanded attribute
    const isExpanded = navMenu.classList.contains('active');
    mobileMenuToggle.setAttribute('aria-expanded', isExpanded);
  });

  // Close mobile menu when clicking on nav links
  navLinks.forEach(link => {
    link.addEventListener('click', () => {
      mobileMenuToggle.classList.remove('active');
      navMenu.classList.remove('active');
      mobileMenuToggle.setAttribute('aria-expanded', 'false');
    });
  });

  // Close mobile menu when clicking outside
  document.addEventListener('click', (e) => {
    if (!navMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
      mobileMenuToggle.classList.remove('active');
      navMenu.classList.remove('active');
      mobileMenuToggle.setAttribute('aria-expanded', 'false');
    }
  });

  // Navbar scroll effect
  window.addEventListener('scroll', () => {
    if (window.scrollY > 100) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  });

  // Smooth scrolling for navigation links
  navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      const href = link.getAttribute('href');
      if (href.startsWith('#')) {
        e.preventDefault();
        const targetId = href.substring(1);
        const targetElement = document.getElementById(targetId);

        if (targetElement) {
          const offsetTop = targetElement.offsetTop - 80; // Account for fixed navbar
          window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
          });

          // Update active link
          navLinks.forEach(l => l.classList.remove('active'));
          link.classList.add('active');
        }
      }
    });
  });

  // Update active link on scroll
  window.addEventListener('scroll', () => {
    const sections = document.querySelectorAll('section[id]');
    const scrollPos = window.scrollY + 100;

    sections.forEach(section => {
      const sectionTop = section.offsetTop;
      const sectionHeight = section.offsetHeight;
      const sectionId = section.getAttribute('id');

      if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
        navLinks.forEach(link => {
          link.classList.remove('active');
          if (link.getAttribute('href') === `#${sectionId}`) {
            link.classList.add('active');
          }
        });
      }
    });
  });
</script>
</html>