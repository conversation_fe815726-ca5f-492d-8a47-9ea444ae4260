@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600&display=swap');

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    text-decoration: none;
    border: none;
    outline: none;
    font-family: 'Poppins', sans-serif;
}

html{
    font-size: 62.5%;
}

body{
    width: 100%;
    height: 100vh;
    overflow-x: hidden;
    background-color: black;
    color: white;
}

/* Enhanced Navbar Styles */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(0, 0, 0, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(230, 139, 4, 0.2);
    z-index: 1000;
    transition: all 0.3s ease;
}

.navbar.scrolled {
    background: rgba(0, 0, 0, 0.98);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Logo Styles */
.logo {
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.logo-text {
    font-size: 2.5rem;
    color: #e68b04;
    font-weight: 800;
    background: linear-gradient(45deg, #e68b04, #ffa726);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    transition: all 0.3s ease;
}

.logo:hover .logo-text {
    transform: scale(1.05);
    filter: brightness(1.2);
}

/* Navigation Menu */
.nav-menu {
    display: flex;
}

.nav-list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    color: #fff;
    text-decoration: none;
    font-size: 2.1rem;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(230, 139, 4, 0.1), transparent);
    transition: left 0.5s ease;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link i {
    font-size: 1rem;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: #e68b04;
    background: rgba(230, 139, 4, 0.1);
    transform: translateY(-2px);
}

.nav-link:hover i,
.nav-link.active i {
    transform: scale(1.1);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 2px;
    background: #e68b04;
    border-radius: 2px;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 30px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001;
}

.hamburger-line {
    width: 100%;
    height: 3px;
    background: #e68b04;
    border-radius: 2px;
    transition: all 0.3s ease;
    transform-origin: center;
}

.mobile-menu-toggle:hover .hamburger-line {
    background: #ffa726;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(1) {
    transform: rotate(45deg) translate(7px, 7px);
}

.mobile-menu-toggle.active .hamburger-line:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active .hamburger-line:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .nav-container {
        padding: 1rem;
    }

    .logo-text {
        font-size: 2rem;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        top: 0;
        right: -100%;
        width: 280px;
        height: 100vh;
        background: rgba(10, 10, 10, 0.98);
        -webkit-backdrop-filter: blur(20px);
        backdrop-filter: blur(20px);
        border-left: 1px solid rgba(230, 139, 4, 0.3);
        transition: right 0.3s ease;
        padding-top: 80px;
        z-index: 999;
    }

    .nav-menu.active {
        right: 0;
    }

    .nav-list {
        flex-direction: column;
        gap: 0;
        padding: 2rem 1rem;
    }

    .nav-link {
        padding: 1.2rem 1.5rem;
        font-size: 1.2rem;
        border-radius: 0;
        border-bottom: 1px solid rgba(230, 139, 4, 0.1);
    }

    .nav-link:hover,
    .nav-link.active {
        background: rgba(230, 139, 4, 0.15);
        transform: translateX(10px);
        border-left: 3px solid #e68b04;
    }

    .nav-link.active::after {
        display: none;
    }

    .nav-link i {
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0.8rem;
    }

    .logo-text {
        font-size: 1.8rem;
    }

    .nav-menu {
        width: 100%;
        right: -100%;
    }

    .nav-list {
        padding: 1.5rem;
    }

    .nav-link {
        padding: 1rem 1.2rem;
        font-size: 1.1rem;
    }
}

section{
    min-height: 100vh;
    padding: 5rem 9% 5rem;
}

.home{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8rem;
    background-color: black;
}

.home .home-content h1{
    font-size: 4.5rem;
    font-weight: 700;
    line-height: 1.3;
}

span{
    color: #e68b04;
}

.home-content h3{
    font-size: 4rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.home-content p{
    font-size: 1.6rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* Home Content Sections */
.intro-section {
    margin-bottom: 4rem;
}

.intro-description {
    max-width: 600px;
    color: #ccc;
    margin-top: 2rem;
}

.about-section {
    margin-bottom: 4rem;
}

.about-section h2 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    line-height: 1.3;
}

.about-content {
    max-width: 700px;
}

.about-content p {
    color: #ccc;
    margin-bottom: 2rem;
    text-align: justify;
}

.contact-section {
    margin-top: 3rem;
}

.home-img img {
    position: relative;
    width: 32vw;
    cursor: pointer;
    border-radius: 12px;
    border: 3px solid #e68b04;
    transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.home-img img:hover {
    box-shadow: 0 0 20px #e68b04;
    transform: scale(1.03);
}


.social-icons a{
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 4rem;
    height: 4rem;
    background-color: transparent;
    border: 0.2rem solid #e68b04;
    font-size: 2rem;
    border-radius: 50%;
    margin: 3rem 1.5rem 3rem 0;
    transition: 0.3s ease;
    color: #e68b04;
}

.social-icons a:hover{
    color: black;
    transform: scale(1.3) translateY(-5px);
    background-color: #e68b04;
    box-shadow: 0  0 25px #e68b04;
}

.btn{
    display: inline-block;
    padding: 1rem 2.8rem;
    background-color: black;
    border-radius: 4rem;
    font-size: 1.6rem;
    color: #e68b04;
    letter-spacing: 0.3rem;
    font-weight: 600;
    border: 2px solid #e68b04;
    transition: 0.3s ease;
    cursor: pointer;
}

.btn:hover{
    transform: scale3d(1.03);
    background-color: #e68b04;
    color: black;
    box-shadow: 0 0 25px #e68b04;
}

.typing-text{
    font-size: 34px;
    font-weight: 600;
    min-width: 280px;
}

.typing-text span{
    position: relative;
}

.typing-text span::before{
    content: "software Developer";
    color: #e68b04;
    animation: words 20s infinite;
}

.typing-text span::after{
    content: "";
    background-color: black;
    position: absolute;
    width: calc(100% + 8px);
    height: 100%;
    border-left: 3px solid black;
    right: -8;
    animation: cursor 0.6s infinite;
}

@keyframes cursor{
    to{
        border-left: 3px solid #e68b04;
    }
}

@keyframes words{
    0%, 20%{
        content: "Web Developer";
    }
    21%, 40%{
        content: "Project Manager";
    }
    41%, 60%{
        content: "Web Designer";
    }
    61%, 80%{
        content: "MERN Stack Developer";
    }
    81%, 100%{
        content: "MEAN Stack Developer";
    }
}

@media (max-width: 1000px){
    .home{
        gap: 4rem;
    }
}

@media(max-width:995px){
    .home{
        flex-direction: column;
        margin: 5rem 4rem;
    }

    .home .home-content h3{
        font-size: 2.5rem;
    }

    .home-content h1{
        font-size: 5rem;
    }

    .about-section h2 {
        font-size: 3rem;
    }

    .intro-section {
        margin-bottom: 3rem;
    }

    .about-section {
        margin-bottom: 3rem;
    }

    .about-content p {
        text-align: left;
    }

    .home-img img{
        width: 70vw;
        margin-top: 4rem;
    }
}

/* Footer Styles */
.site-footer {
  background: linear-gradient(135deg, #0a0a0a 0%, #111 100%);
  padding: 5rem 9% 2rem;
  font-size: 1.3rem;
  line-height: 1.6;
  border-top: 2px solid rgba(230,139,4,0.3);
  position: relative;
}

.site-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e68b04, transparent);
}

.footer-inner {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  margin-bottom: 3rem;
  padding-bottom: 3rem;
  border-bottom: 1px solid rgba(230,139,4,0.2);
}

/* Footer Brand */
.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.footer-logo {
  font-size: 2.5rem;
  color: #e68b04;
  font-weight: 800;
  margin: 0;
  margin-bottom: 0.5rem;
}

.footer-name {
  font-weight: 600;
  font-size: 1.4rem;
  color: #fff;
  margin: 0;
}

.footer-status {
  color: #bbb;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin: 0;
}

.status-indicator {
  width: 8px;
  height: 8px;
  background: #4ade80;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Footer Links */
.footer-links {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
}

.footer-contact h4,
.footer-social h4 {
  color: #e68b04;
  font-size: 1.6rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  margin-top: 0;
}

/* Contact Info */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.contact-email,
.contact-location {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #ccc;
  transition: all 0.3s ease;
}

.contact-email {
  text-decoration: none;
  padding: 0.8rem 0;
  border-radius: 8px;
}

.contact-email:hover {
  color: #e68b04;
  transform: translateX(5px);
}

.contact-email i,
.contact-location i {
  width: 20px;
  color: #e68b04;
  font-size: 1.1rem;
}

/* Social Links */
.social-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.social-links a {
  display: flex;
  align-items: center;
  gap: 1rem;
  color: #ccc;
  text-decoration: none;
  padding: 0.8rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(230,139,4,0.2);
  transition: all 0.3s ease;
  background: rgba(230,139,4,0.05);
}

.social-links a:hover {
  background: rgba(230,139,4,0.1);
  border-color: #e68b04;
  color: #e68b04;
  transform: translateX(5px);
}

.social-links a i {
  width: 20px;
  font-size: 1.2rem;
}

/* Footer Bottom */
.footer-bottom {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
  text-align: center;
}

.footer-tech {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.tech-label {
  color: #e68b04;
  font-weight: 600;
  font-size: 1.2rem;
}

.tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  justify-content: center;
}

.tech-item {
  background: rgba(230,139,4,0.1);
  color: #e68b04;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 1rem;
  font-weight: 500;
  border: 1px solid rgba(230,139,4,0.3);
  transition: all 0.3s ease;
}

.tech-item:hover {
  background: rgba(230,139,4,0.2);
  transform: translateY(-2px);
}

.footer-note {
  color: #888;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
}

.footer-note i {
  color: #e68b04;
  animation: heartbeat 2s infinite;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .footer-main {
    grid-template-columns: 1fr;
    gap: 3rem;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .footer-bottom {
    text-align: center;
  }

  .tech-stack {
    justify-content: center;
  }

  .social-links a {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .site-footer {
    padding: 3rem 5% 2rem;
  }

  .footer-logo {
    font-size: 2rem;
  }

  .footer-main {
    gap: 2rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
  }

  .footer-links {
    gap: 1.5rem;
  }

  .tech-stack {
    gap: 0.5rem;
  }

  .tech-item {
    padding: 0.3rem 0.8rem;
    font-size: 0.9rem;
  }
}

/* Education Page Styles */
.education-page {
  padding-top: 80px;
  min-height: 100vh;
}

.education-hero {
  background: linear-gradient(135deg, #000 0%, #111 50%, #000 100%);
  padding: 8rem 9% 6rem;
  text-align: center;
  position: relative;
}

.education-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e68b04" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-content h1 {
  font-size: 5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  color: #fff;
}

.hero-subtitle {
  font-size: 1.8rem;
  color: #ccc;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section-title {
  font-size: 3.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 4rem;
  color: #fff;
}

/* Timeline Styles */
.education-timeline {
  padding: 8rem 0;
  background: #0a0a0a;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline::before {
  content: '';
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, #e68b04, #ffa726);
}

.timeline-item {
  position: relative;
  margin-bottom: 4rem;
  padding-left: 80px;
}

.timeline-marker {
  position: absolute;
  left: 15px;
  top: 0;
  width: 30px;
  height: 30px;
  background: #e68b04;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000;
  font-size: 1.2rem;
  font-weight: bold;
  box-shadow: 0 0 20px rgba(230, 139, 4, 0.5);
}

.timeline-content {
  background: rgba(230, 139, 4, 0.05);
  border: 1px solid rgba(230, 139, 4, 0.2);
  border-radius: 12px;
  padding: 2.5rem;
  transition: all 0.3s ease;
}

.timeline-content:hover {
  background: rgba(230, 139, 4, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(230, 139, 4, 0.2);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.timeline-header h3 {
  font-size: 2rem;
  font-weight: 600;
  color: #e68b04;
  margin: 0;
}

.timeline-date {
  background: rgba(230, 139, 4, 0.2);
  color: #e68b04;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
  font-size: 1rem;
}

.institution {
  font-size: 1.4rem;
  font-weight: 600;
  color: #fff;
  margin: 0.5rem 0;
}

.location {
  color: #ccc;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.education-details,
.achievements {
  margin-top: 2rem;
}

.education-details h5,
.achievements h5 {
  color: #e68b04;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.coursework-list,
.achievement-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.coursework-list li,
.achievement-list li {
  color: #ccc;
  padding: 0.5rem 0;
  padding-left: 2rem;
  position: relative;
  line-height: 1.6;
}

.coursework-list li::before {
  content: '▸';
  color: #e68b04;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.achievement-list li::before {
  content: '★';
  color: #e68b04;
  position: absolute;
  left: 0;
}

/* Certifications Section */
.certifications {
  padding: 8rem 0;
  background: #111;
}

.cert-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.cert-card {
  background: rgba(230, 139, 4, 0.05);
  border: 1px solid rgba(230, 139, 4, 0.2);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cert-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(230, 139, 4, 0.1), transparent);
  transition: left 0.5s ease;
}

.cert-card:hover::before {
  left: 100%;
}

.cert-card:hover {
  background: rgba(230, 139, 4, 0.1);
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(230, 139, 4, 0.2);
}

.cert-icon {
  font-size: 3rem;
  color: #e68b04;
  margin-bottom: 1.5rem;
}

.cert-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 1rem;
}

.cert-card p {
  color: #ccc;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.cert-status {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
}

.cert-status:contains("Completed") {
  background: rgba(76, 175, 80, 0.2);
  color: #4caf50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.cert-status:contains("In Progress") {
  background: rgba(255, 193, 7, 0.2);
  color: #ffc107;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.cert-status:contains("Ongoing") {
  background: rgba(33, 150, 243, 0.2);
  color: #2196f3;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

/* Skills Acquired Section */
.skills-acquired {
  padding: 8rem 0;
  background: #0a0a0a;
}

.skills-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  margin-top: 3rem;
}

.skill-category {
  background: rgba(230, 139, 4, 0.05);
  border: 1px solid rgba(230, 139, 4, 0.2);
  border-radius: 12px;
  padding: 2.5rem;
  transition: all 0.3s ease;
}

.skill-category:hover {
  background: rgba(230, 139, 4, 0.1);
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(230, 139, 4, 0.2);
}

.skill-category h3 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #e68b04;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.skill-category h3 i {
  font-size: 1.5rem;
}

.skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.skill-tag {
  background: rgba(230, 139, 4, 0.1);
  color: #e68b04;
  padding: 0.6rem 1.2rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  border: 1px solid rgba(230, 139, 4, 0.3);
  transition: all 0.3s ease;
  cursor: default;
}

.skill-tag:hover {
  background: rgba(230, 139, 4, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(230, 139, 4, 0.3);
}

/* Education Page Responsive Styles */
@media (max-width: 768px) {
  .education-hero {
    padding: 6rem 5% 4rem;
  }

  .hero-content h1 {
    font-size: 3.5rem;
  }

  .hero-subtitle {
    font-size: 1.4rem;
  }

  .section-title {
    font-size: 2.5rem;
  }

  .education-timeline,
  .certifications,
  .skills-acquired {
    padding: 5rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .timeline::before {
    left: 20px;
  }

  .timeline-item {
    padding-left: 60px;
  }

  .timeline-marker {
    left: 5px;
    width: 25px;
    height: 25px;
    font-size: 1rem;
  }

  .timeline-content {
    padding: 2rem;
  }

  .timeline-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .timeline-header h3 {
    font-size: 1.6rem;
  }

  .cert-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .skills-categories {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .skill-category {
    padding: 2rem;
  }

  .skill-category h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .education-hero {
    padding: 4rem 3% 3rem;
  }

  .hero-content h1 {
    font-size: 2.8rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .timeline-content {
    padding: 1.5rem;
  }

  .timeline-header h3 {
    font-size: 1.4rem;
  }

  .institution {
    font-size: 1.2rem;
  }

  .cert-card {
    padding: 1.5rem;
  }

  .skill-category {
    padding: 1.5rem;
  }

  .skill-tags {
    gap: 0.8rem;
  }

  .skill-tag {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}
