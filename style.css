@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600&display=swap');

*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    text-decoration: none;
    border: none;
    outline: none;
    font-family: 'Poppins', sans-serif;
}

html{
    font-size: 62.5%;
}

body{
    width: 100%;
    height: 100vh;
    overflow-x: hidden;
    background-color: black;
    color: white;
}

header{
    margin-top: 20px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 1rem 9%;
    background-color: transparent;
    filter: drop-shadow(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
}

.logo{
    font-size: 3rem;
    color: #e68b04;
    font-weight: 800;
    cursor: pointer;
    transition: 0.5s ease;
}

.logo:hover{
    transform: scale(1.3);
}

nav a{
    font-size: 1.8rem;
    color: white;
    margin-left: 4rem;
    font-weight: 500;
    transition: 0.3s ease;
    border-bottom: 3px solid transparent;
}

nav a:hover,
nav a.active{
    color: #e68b04;
    border-bottom: 3px solid #e68b04;
}

@media(max-width:995px){
    nav{
        position: absolute;
        display: none;
        top: 0;
        right: 0;
        width: 40%;
        border-left: 3px solid #e68b04;
        border-bottom: 3px solid #e68b04;
        border-bottom-left-radius: 2rem;
        padding: 1rem solid;
        background-color: #161616;
        border-top: 0.1rem solid rgba(0,0,0,0.1);
    }

    nav.active{
        display: block;
    }

    nav a{
        display: block;
        font-size: 2rem;
        margin: 3rem 0;
    }

    nav a:hover,
    nav a.active{
        padding: 1rem;
        border-radius: 0.5rem;
        border-bottom: 0.5rem solid #e68b04;
    }
}

section{
    min-height: 100vh;
    padding: 5rem 9% 5rem;
}

.home{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8rem;
    background-color: black;
}

.home .home-content h1{
    font-size: 6rem;
    font-weight: 700;
    line-height: 1.3;
}

span{
    color: #e68b04;
}

.home-content h3{
    font-size: 4rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.home-content p{
    font-size: 1.6rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

/* Home Content Sections */
.intro-section {
    margin-bottom: 4rem;
}

.intro-description {
    max-width: 600px;
    color: #ccc;
    margin-top: 2rem;
}

.about-section {
    margin-bottom: 4rem;
}

.about-section h2 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    line-height: 1.3;
}

.about-content {
    max-width: 700px;
}

.about-content p {
    color: #ccc;
    margin-bottom: 2rem;
    text-align: justify;
}

.contact-section {
    margin-top: 3rem;
}

.home-img img {
    position: relative;
    width: 32vw;
    cursor: pointer;
    border-radius: 12px;
    border: 3px solid #e68b04;
    transition: box-shadow 0.3s ease, transform 0.3s ease;
}

.home-img img:hover {
    box-shadow: 0 0 20px #e68b04;
    transform: scale(1.03);
}


.social-icons a{
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 4rem;
    height: 4rem;
    background-color: transparent;
    border: 0.2rem solid #e68b04;
    font-size: 2rem;
    border-radius: 50%;
    margin: 3rem 1.5rem 3rem 0;
    transition: 0.3s ease;
    color: #e68b04;
}

.social-icons a:hover{
    color: black;
    transform: scale(1.3) translateY(-5px);
    background-color: #e68b04;
    box-shadow: 0  0 25px #e68b04;
}

.btn{
    display: inline-block;
    padding: 1rem 2.8rem;
    background-color: black;
    border-radius: 4rem;
    font-size: 1.6rem;
    color: #e68b04;
    letter-spacing: 0.3rem;
    font-weight: 600;
    border: 2px solid #e68b04;
    transition: 0.3s ease;
    cursor: pointer;
}

.btn:hover{
    transform: scale3d(1.03);
    background-color: #e68b04;
    color: black;
    box-shadow: 0 0 25px #e68b04;
}

.typing-text{
    font-size: 34px;
    font-weight: 600;
    min-width: 280px;
}

.typing-text span{
    position: relative;
}

.typing-text span::before{
    content: "software Developer";
    color: #e68b04;
    animation: words 20s infinite;
}

.typing-text span::after{
    content: "";
    background-color: black;
    position: absolute;
    width: calc(100% + 8px);
    height: 100%;
    border-left: 3px solid black;
    right: -8;
    animation: cursor 0.6s infinite;
}

@keyframes cursor{
    to{
        border-left: 3px solid #e68b04;
    }
}

@keyframes words{
    0%, 20%{
        content: "Web Developer";
    }
    21%, 40%{
        content: "Project Manager";
    }
    41%, 60%{
        content: "Web Designer";
    }
    61%, 80%{
        content: "MERN Stack Developer";
    }
    81%, 100%{
        content: "MEAN Stack Developer";
    }
}

@media (max-width: 1000px){
    .home{
        gap: 4rem;
    }
}

@media(max-width:995px){
    .home{
        flex-direction: column;
        margin: 5rem 4rem;
    }

    .home .home-content h3{
        font-size: 2.5rem;
    }

    .home-content h1{
        font-size: 5rem;
    }

    .about-section h2 {
        font-size: 3rem;
    }

    .intro-section {
        margin-bottom: 3rem;
    }

    .about-section {
        margin-bottom: 3rem;
    }

    .about-content p {
        text-align: left;
    }

    .home-img img{
        width: 70vw;
        margin-top: 4rem;
    }
}

.site-footer {
  background: #0a0a0a;
  padding: 4rem 9% 3rem;
  font-size: 1.3rem;
  line-height: 1.5;
  border-top: 1px solid rgba(230,139,4,0.25);
}

.site-footer a {
  color: #e68b04;
  transition: color .25s;
}

.site-footer a:hover,
.site-footer a:focus-visible {
  color: #fff;
}

.footer-inner {
  display: grid;
  gap: 1.2rem;
  justify-items: start;
}

.footer-contact,
.footer-social {
  display: flex;
  gap: 1.2rem;
  flex-wrap: wrap;
  align-items: center;
}

.footer-social a {
  font-size: 1.1rem;
  padding: .6rem .9rem;
  border: 1px solid #e68b04;
  border-radius: 999px;
  line-height: 1;
}

.footer-social a:hover {
  background:#e68b04;
  color:#000;
}

.footer-name {
  font-weight: 600;
  font-size: 1.4rem;
}

.footer-status {
  color: #bbb;
  font-size: 1.2rem;
}

.footer-tech,
.footer-note {
  color: #888;
  font-size: 1.1rem;
}

.dot {
  opacity: .5;
}

@media (min-width: 720px) {
  .footer-inner {
    grid-template-columns: repeat(auto-fit,minmax(220px,1fr));
    align-items: start;
  }
  .footer-tech,
  .footer-note {
    grid-column: 1 / -1;
  }
}
