<!DOCTYPE html>
<html lang="en">
    
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <title>Education - Miguel <PERSON></title>
</head>
<body>
    <header class="navbar">
        <div class="nav-container">
            <a href="index.html" class="logo" aria-label="<PERSON> V Tan - Home">
                <span class="logo-text">@leugimnat</span>
            </a>
            
            <nav class="nav-menu" role="navigation" aria-label="Main navigation">
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">
                            <i class="fa-solid fa-house"></i>
                            <span>Home</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="education.html" class="nav-link active" aria-current="page">
                            <i class="fa-solid fa-graduation-cap"></i>
                            <span>Education</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="experience.html" class="nav-link">
                            <i class="fa-solid fa-briefcase"></i>
                            <span>Experience</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="portfolio.html" class="nav-link">
                            <i class="fa-solid fa-folder-open"></i>
                            <span>Portfolio</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#contact" class="nav-link">
                            <i class="fa-solid fa-envelope"></i>
                            <span>Contact</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <button type="button" class="mobile-menu-toggle" aria-label="Toggle mobile menu" aria-expanded="false">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </header>

    <main class="education-page">
        <section class="education-hero">
            <div class="hero-content">
                <h1>My Educational <span>Journey</span></h1>
                <p class="hero-subtitle">After I did NAT and NCAE, I realized I was within range to a lot of opportunities in college. My mother wanted me to be a doctor, but I wanted to be an IT, so I chose Bachelor of Science and Information Technology.</p>
                <p class="hero-subtitle">The first few years were rough, it was learning java, object-oriented-programming, databases, theories and etc. However it quicly took up pace during 3rd year and 4th year wherein I was able to apply what I've learned into actualy projects.</p>
                <p class="hero-subtitle">Throughout my academic and internship experience, I’ve built strong foundations in Python, JavaScript, MySQL, and network systems, which I applied in both backend and frontend development projects. One of my most significant contributions was to our university capstone project, “RequeXU: An Adaptive Web Application for Streamlined Multi-Campus Work Order and Dispatch Management,” which was successfully deployed in a production environment for our university's Physical Plant Office. This involved developing clean, scalable APIs, integrating external services, and managing real-time data</p>
                <p class="hero-subtitle">I also interned with M. Montesclaros Holdings Inc., where I worked in the technical IT team and further developed my ability to communicate with stakeholders, support system improvements, and apply logic to resolve technical challenges. These experiences helped me build a strong foundation in agile planning, backlog management, testing, and pair programming</p>
            </div>
        </section>

        <section class="education-timeline">
            <div class="container">
                <h2 class="section-title">Academic <span>Background</span></h2>
                
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker">
                            <i class="fa-solid fa-graduation-cap"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <h3>Bachelor of Science in Information Technology</h3>
                                <span class="timeline-date">2020 - 2024</span>
                            </div>
                            <h4 class="institution">Xavier University - Ateneo de Cagayan</h4>
                            <p class="location">Cagayan de Oro City, Philippines</p>
                            <div class="education-details">
                                <h5>Key Coursework:</h5>
                                <ul class="coursework-list">
                                    <li>Data Structures and Algorithms</li>
                                    <li>Database Management Systems</li>
                                    <li>Web Development Technologies</li>
                                    <li>Network Administration</li>
                                    <li>Software Engineering</li>
                                    <li>Project Management</li>
                                    <li>Systems Analysis and Design</li>
                                    <li>Mobile Application Development</li>
                                </ul>
                            </div>
                            <div class="achievements">
                                <h5>Notable Achievements:</h5>
                                <ul class="achievement-list">
                                    <li>Dean's List recognition for academic excellence</li>
                                    <li>Completed capstone project on web-based management system</li>
                                    <li>Active participation in IT-related seminars and workshops</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-marker">
                            <i class="fa-solid fa-school"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <h3>Senior High School - STEM Track</h3>
                                <span class="timeline-date">2018 - 2020</span>
                            </div>
                            <h4 class="institution">Xavier University - Ateneo de Cagayan</h4>
                            <p class="location">Cagayan de Oro City, Philippines</p>
                            <div class="education-details">
                                <h5>Specialized Subjects:</h5>
                                <ul class="coursework-list">
                                    <li>Computer Programming (Introduction to coding)</li>
                                    <li>Research and Statistics</li>
                                    <li>Advanced Mathematics</li>
                                    <li>Physics and Chemistry</li>
                                    <li>ICT and Digital Literacy</li>
                                </ul>
                            </div>
                            <div class="achievements">
                                <h5>Highlights:</h5>
                                <ul class="achievement-list">
                                    <li>First exposure to programming through Minecraft coding exercises</li>
                                    <li>Developed strong analytical and problem-solving skills</li>
                                    <li>Participated in science and technology competitions</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="certifications">
            <div class="container">
                <h2 class="section-title">Certifications & <span>Learning</span></h2>
                <div class="cert-grid">
                    <div class="cert-card">
                        <div class="cert-icon">
                            <i class="fa-brands fa-html5"></i>
                        </div>
                        <h3>Web Development</h3>
                        <p>Self-taught HTML, CSS, JavaScript, and modern frameworks</p>
                        <span class="cert-status">Ongoing</span>
                    </div>
                    
                    <div class="cert-card">
                        <div class="cert-icon">
                            <i class="fa-solid fa-database"></i>
                        </div>
                        <h3>Database Management</h3>
                        <p>MySQL, MongoDB, and database design principles</p>
                        <span class="cert-status">Completed</span>
                    </div>
                    
                    <div class="cert-card">
                        <div class="cert-icon">
                            <i class="fa-brands fa-react"></i>
                        </div>
                        <h3>MERN Stack Development</h3>
                        <p>MongoDB, Express.js, React, Node.js full-stack development</p>
                        <span class="cert-status">In Progress</span>
                    </div>
                    
                    <div class="cert-card">
                        <div class="cert-icon">
                            <i class="fa-solid fa-network-wired"></i>
                        </div>
                        <h3>Network Administration</h3>
                        <p>Network configuration, security, and troubleshooting</p>
                        <span class="cert-status">Completed</span>
                    </div>
                </div>
            </div>
        </section>

        <section class="skills-acquired">
            <div class="container">
                <h2 class="section-title">Skills <span>Acquired</span></h2>
                <div class="skills-categories">
                    <div class="skill-category">
                        <h3><i class="fa-solid fa-code"></i> Programming Languages</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">JavaScript</span>
                            <span class="skill-tag">Python</span>
                            <span class="skill-tag">Java</span>
                            <span class="skill-tag">C++</span>
                            <span class="skill-tag">PHP</span>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h3><i class="fa-solid fa-globe"></i> Web Technologies</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">HTML5</span>
                            <span class="skill-tag">CSS3</span>
                            <span class="skill-tag">React</span>
                            <span class="skill-tag">Node.js</span>
                            <span class="skill-tag">Express.js</span>
                        </div>
                    </div>
                    
                    <div class="skill-category">
                        <h3><i class="fa-solid fa-database"></i> Database & Tools</h3>
                        <div class="skill-tags">
                            <span class="skill-tag">MySQL</span>
                            <span class="skill-tag">MongoDB</span>
                            <span class="skill-tag">Git</span>
                            <span class="skill-tag">VS Code</span>
                            <span class="skill-tag">Figma</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="site-footer">
        <div class="footer-inner">
            <div class="footer-main">
                <div class="footer-brand">
                    <h3 class="footer-logo">@leugimnat</h3>
                    <p class="footer-name">&copy; <span id="year"></span> Miguel Antonio V Tan</p>
                    <p class="footer-status">
                        <span class="status-indicator"></span>
                        Open to Front-End / Full-Stack Roles
                    </p>
                </div>

                <div class="footer-links">
                    <div class="footer-contact">
                        <h4>Get In Touch</h4>
                        <div class="contact-info">
                            <a href="mailto:<EMAIL>" class="contact-email">
                                <i class="fa-solid fa-envelope"></i>
                                <EMAIL>
                            </a>
                            <div class="contact-location">
                                <i class="fa-solid fa-location-dot"></i>
                                <span>Cagayan de Oro City, Philippines</span>
                            </div>
                        </div>
                    </div>

                    <div class="footer-social">
                        <h4>Connect</h4>
                        <div class="social-links">
                            <a href="https://github.com/leugimnat" aria-label="GitHub Profile" title="GitHub">
                                <i class="fa-brands fa-github"></i>
                                <span>GitHub</span>
                            </a>
                            <a href="https://www.linkedin.com/in/miguel-antonio-tan-945499255/" aria-label="LinkedIn Profile" title="LinkedIn">
                                <i class="fa-brands fa-linkedin"></i>
                                <span>LinkedIn</span>
                            </a>
                            <a href="https://www.facebook.com/zeuqzavleugimnat/" aria-label="Facebook Profile" title="Facebook">
                                <i class="fa-brands fa-facebook"></i>
                                <span>Facebook</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-tech">
                    <span class="tech-label">Tech Stack:</span>
                    <div class="tech-stack">
                        <span class="tech-item">HTML</span>
                        <span class="tech-item">CSS</span>
                        <span class="tech-item">JavaScript</span>
                        <span class="tech-item">MERN</span>
                        <span class="tech-item">MEAN</span>
                    </div>
                </div>
                <p class="footer-note">
                    <i class="fa-solid fa-heart"></i>
                    Crafted with care • Accessible & responsive • Last updated Aug 2025
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Update year
        document.getElementById('year').textContent = new Date().getFullYear();
        
        // Mobile menu functionality
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const navMenu = document.querySelector('.nav-menu');
        const navLinks = document.querySelectorAll('.nav-link');
        const navbar = document.querySelector('.navbar');
        
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', () => {
            mobileMenuToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
            
            // Update aria-expanded attribute
            const isExpanded = navMenu.classList.contains('active');
            mobileMenuToggle.setAttribute('aria-expanded', isExpanded);
        });
        
        // Close mobile menu when clicking on nav links
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                mobileMenuToggle.classList.remove('active');
                navMenu.classList.remove('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'false');
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!navMenu.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                mobileMenuToggle.classList.remove('active');
                navMenu.classList.remove('active');
                mobileMenuToggle.setAttribute('aria-expanded', 'false');
            }
        });
        
        // Navbar scroll effect
        window.addEventListener('scroll', () => {
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    </script>
</body>
</html>
